import * as React from "react";
import styles from "./KeyUserInfo.module.scss";
import { IKeyUserInfoProps } from "./IKeyUserInfoProps";

interface DocumentItem {
  Title: string;
  Videos: string; // Document URL
  PreviewUrl: string; // First-page preview image URL
}
interface SharePointRawItem {
  Title: string;
  Content?: {
    Url?: string;
  };
}

const siteUrl = "https://corptb.sharepoint.com/sites/ProjectEngLand";
const listName = "platontestcasesexecution";

const defaultImage =
  "https://upload.wikimedia.org/wikipedia/commons/thumb/8/87/PDF_file_icon.svg/400px-PDF_file_icon.svg.png";

const TestCases: React.FC<IKeyUserInfoProps> = (props) => {
  React.useEffect(() => {
    const fetchDocuments = async (): Promise<void> => {
      try {
        const lists = [listName, "platontestcasesguide"];

        const allDocuments: DocumentItem[] = [];

        for (const list of lists) {
          const response = await fetch(
            `${siteUrl}/_api/web/lists/getbytitle('${list}')/items?$select=Title,Content`,
            {
              headers: { Accept: "application/json;odata=verbose" },
              credentials: "include",
            }
          );

          const data: { d: { results: SharePointRawItem[] } } =
            await response.json();

          const documents: DocumentItem[] = data.d.results.map(
            (item: SharePointRawItem) => {
              const docUrl = item.Content?.Url || "";
              const previewUrl = `/_layouts/15/getpreview.ashx?path=${encodeURIComponent(
                docUrl
              )}`;
              return {
                Title: item.Title,
                Videos: docUrl,
                PreviewUrl: previewUrl,
              };
            }
          );

          allDocuments.push(...documents);
        }

        setDocData(allDocuments);
      } catch (error) {
        console.error("Error fetching documents:", error);
      }
    };

    fetchDocuments().catch((error) => {
      console.error("Unhandled error in fetchDocuments:", error);
    });
  }, [siteUrl, listName]);

  return (
    <>
      <div className={`${"section-common sectionHandbooks"}`}>

        <div className={`${styles.videoCarousel} ${"sectionHandbookPrimary"}`}>
          <div
            className={`${
              styles.carouselContainer
            } ${"sectionHandbookContainer"}`}
          >
            <div className={`${styles.videoGrid} ${"sectionHandbookCarousel"}`}>
              {filteredVideos.slice(0, visibleCount).map((item, index) => (
                <div className={styles.card} key={index}>
                  <div className={`sectionHandbookCardContent`}>
                    <div className="contentImageWrapper">
                      <a
                        href={`${item.Videos}?web=1`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {item.Videos.endsWith(".mp4") ||
                        item.Videos.endsWith(".wmv") ? (
                          <video
                            controls
                            className={styles.previewImage}
                            poster={
                              item.PreviewUrl || "/assets/video-placeholder.png"
                            }
                          >
                            <source src={item.Videos} type="video/mp4" />
                          </video>
                        ) : (
                          <img
                            src={defaultImage}
                            alt={`Preview of ${item.Title}`}
                            className={styles.previewImage}
                          />
                        )}
                      </a>
                    </div>
                    <div className={`section-handbook-text-info`}>
                      <h3>{item.Title}</h3>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TestCases;
